package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.inquirypayment

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Button
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.google.gson.Gson
import kotlinx.coroutines.launch
import timber.log.Timber
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputFieldBase
import vn.com.bidv.designsystem.component.datepicker.IBankDatePickerDialog
import vn.com.bidv.designsystem.component.datepicker.IBankInputDatePicker
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBankTheme
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreReducer
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseScreen
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ActionType
import vn.com.bidv.feature.government.service.common.Constants
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.ui.common.ItemCardBody
import vn.com.bidv.feature.government.service.ui.common.ItemCardCommonWithModifier
import vn.com.bidv.feature.government.service.ui.common.ItemCardHeader
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.data.ShareDataDTO
import java.util.Calendar

/**
 * vấn tin khoản nộp
 */
@Composable
fun InquiryTransactionScreen(
    taxNumber: String,
    navHostController: NavHostController
) {
    val viewModel: InquiryTransactionViewModel = hiltViewModel()
    val loadListViewModel: LoadListInquiryTransactionViewModel = hiltViewModel()
    val lazyListState: LazyListState = rememberLazyListState()
    val scope = rememberCoroutineScope()

    // Set the tax number in the load list view model
    LaunchedEffect(taxNumber) {
        loadListViewModel.taxNumber = taxNumber
    }

    BaseScreen(
        navController = navHostController,
        viewModel = viewModel,
        topAppBarConfig = TopAppBarConfig(
            showHomeIcon = true,
            titleTopAppBar = stringResource(R.string.thue_phi_hai_quan)
        ),
        handleSideEffect = { },
    ) { viewState, viewEvent ->
        var isDeclarationNumberFocused by remember { mutableStateOf(false) }
        val currentRuleFilter = viewState.ruleFilters ?: InquiryTransactionRuleFilter()
        val selectedItemsCount = viewState.listData?.count { it.isChecked } ?: 0

        Box(modifier = Modifier.fillMaxSize()) {
            Column {
                InquiryFormContent(
                    taxNumber = taxNumber,
                    ruleFilter = currentRuleFilter,
                    onUpdateFilter = { newFilter ->
                        viewEvent(
                            vn.com.bidv.feature.common.ui.screen.commonlisttransaction.ListTransactionBaseReducer.ListTransactionBaseViewEvent.UpdateRuleFilter(
                                newFilter
                            )
                        )
                    },
                    onDeclarationNumberFocusChanged = { isFocused ->
                        isDeclarationNumberFocused = isFocused
                    }
                )

                ListTransactionBaseScreen(
                    viewModel = viewModel,
                    navController = navHostController,
                    lazyListState = lazyListState,
                    loadListViewModel = loadListViewModel,
                    shouldShowSearchArea = false,
                    shouldShowCreateButton = false,
                    shouldShowBottomAction = false,
                    ruleFilterDefault = InquiryTransactionRuleFilter(),
                    reloadKey = Constants.INQUIRY_TRANSACTION_RELOAD_KEY,
                    listAction = emptyList(),
                    handleAction = { _, _ -> }
                ) { item, onEventListView ->
                    ItemCardCommonWithModifier<InquiryCustomsDutyDMO, InquiryTransactionRuleFilter>(
                        transactionsDMO = item,
                        uiState = viewState,
                        onEventListView = onEventListView,
                        handleAction = { },
                        contentHeader = { model ->
                            ItemCardHeader(model.data)
                        },
                        contentBody = { model ->
                            ItemCardBody(model.data)
                        }
                    )
                }
            }

            BottomButtonArea(
                isDeclarationNumberFocused = isDeclarationNumberFocused,
                declarationNumber = currentRuleFilter.taxDeclarationNo,
                selectedItemsCount = selectedItemsCount,
                onInquiry = {
                    viewModel.requestReloadData(Constants.INQUIRY_TRANSACTION_RELOAD_KEY)
                },
                onSubmitSelection = {
                    scope.launch {
                        navHostController.popBackStack()
                        val selectedItems = viewState.listData?.filter { it.isChecked }?.map { it.data } ?: emptyList()
                        viewModel.localRepository.shareDataTo(
                            InquiryTransactionScreenConstants.SHAREDATA_KEY,
                            ShareDataDTO(
                                InquiryTransactionScreenConstants.SHAREDATA_KEY,
                                Gson().toJson(selectedItems)
                            )
                        )
                    }
                },
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

@Composable
private fun InquiryFormContent(
    taxNumber: String,
    ruleFilter: InquiryTransactionRuleFilter,
    onUpdateFilter: (InquiryTransactionRuleFilter) -> Unit,
    onDeclarationNumberFocusChanged: (Boolean) -> Unit
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    var showDatePickerDialog by remember { mutableStateOf(false) }

    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding(IBSpacing.spacingM)
    ) {
        IBankInputFieldBase(
            text = ruleFilter.taxDeclarationNo,
            placeholderText = stringResource(R.string.so_to_khai_hai_quan),
            modifier = Modifier.onFocusChanged { focusState ->
                onDeclarationNumberFocusChanged(focusState.isFocused)
            }
        ) {
            onUpdateFilter(ruleFilter.copy(taxDeclarationNo = it.text))
        }

        IBankInputDatePicker(
            state = if (ruleFilter.taxDeclarationNo.isEmpty())
                IBFrameState.DISABLE(colorScheme)
            else
                IBFrameState.DEFAULT(colorScheme),
            labelText = "Năm đăng ký",
            text = ruleFilter.taxDeclarationYear?.toString() ?: "",
            onClickClear = {
                onUpdateFilter(ruleFilter.copy(taxDeclarationYear = null))
            },
            onClickEnd = {
                if (ruleFilter.taxDeclarationNo.isNotEmpty()) {
                    showDatePickerDialog = true
                }
            }
        )
    }

    if (showDatePickerDialog) {
        IBankDatePickerDialog(
            modifier = Modifier,
            title = "Chọn năm đăng ký",
            dateSelected = Calendar.getInstance().apply {
                set(Calendar.DAY_OF_YEAR, 1)
                set(Calendar.MONTH, 0)
                ruleFilter.taxDeclarationYear?.let {
                    set(Calendar.YEAR, it)
                }
            }.time,
            onDismissRequest = {
                showDatePickerDialog = false
            }
        ) { date ->
            date?.let {
                val year = Calendar.getInstance().apply {
                    time = it
                }.get(Calendar.YEAR)
                onUpdateFilter(ruleFilter.copy(taxDeclarationYear = year))
            }
            showDatePickerDialog = false
        }
    }
}



@Preview
@Composable
private fun PrevInquiryFormContent() {
    IBankTheme {
        Surface {
            InquiryFormContent(
                taxNumber = "*********",
                ruleFilter = InquiryTransactionRuleFilter(
                    taxDeclarationNo = "***********",
                    taxDeclarationYear = 2025
                ),
                onUpdateFilter = { },
                onDeclarationNumberFocusChanged = { }
            )
        }
    }
}

@Composable
private fun BottomButtonArea(
    isDeclarationNumberFocused: Boolean,
    declarationNumber: String,
    selectedItemsCount: Int,
    onInquiry: () -> Unit,
    onSubmitSelection: () -> Unit,
    modifier: Modifier = Modifier
) {
    val colorScheme = LocalColorScheme.current

    val showInquiryButton = isDeclarationNumberFocused
    val showSubmitButton = !isDeclarationNumberFocused && selectedItemsCount > 0
    val isInquiryEnabled = declarationNumber.isNotEmpty()

    if (showInquiryButton || showSubmitButton) {
        Row(
            modifier = modifier
                .fillMaxWidth()
                .background(colorScheme.bgMainTertiary)
                .padding(IBSpacing.spacingM)
        ) {
            when {
                showInquiryButton -> {
                    IBankNormalButton(
                        modifier = Modifier.fillMaxWidth(),
                        type = if (isInquiryEnabled) {
                            NormalButtonType.PRIMARY(colorScheme)
                        } else {
                            NormalButtonType.DISABLE(colorScheme)
                        },
                        text = "Inquiry",
                        enabled = isInquiryEnabled,
                        onClick = onInquiry
                    )
                }
                showSubmitButton -> {
                    IBankNormalButton(
                        modifier = Modifier.fillMaxWidth(),
                        type = NormalButtonType.PRIMARY(colorScheme),
                        text = stringResource(R.string.them_khoan_nop),
                        onClick = onSubmitSelection
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun PrevBottomButtonArea() {
    IBankTheme {
        Column {
            Surface {
                BottomButtonArea(
                    isDeclarationNumberFocused = true,
                    declarationNumber = "123456",
                    selectedItemsCount = 0,
                    onInquiry = { },
                    onSubmitSelection = { }
                )
            }
            Surface {
                BottomButtonArea(
                    isDeclarationNumberFocused = true,
                    declarationNumber = "",
                    selectedItemsCount = 0,
                    onInquiry = { },
                    onSubmitSelection = { }
                )
            }
            Surface {
                BottomButtonArea(
                    isDeclarationNumberFocused = false,
                    declarationNumber = "123456",
                    selectedItemsCount = 2,
                    onInquiry = { },
                    onSubmitSelection = { }
                )
            }
        }
    }
}

object InquiryTransactionScreenConstants {
    const val SHAREDATA_KEY = "inquiry_transaction_screen_share_data_key"
}