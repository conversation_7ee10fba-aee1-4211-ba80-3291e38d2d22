package vn.com.bidv.feature.government.service.common

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.component.datepicker.model.DatePickerConfig
import java.util.Calendar
import java.util.Date

/**
 * Example usage of IBankYearPicker component
 *
 * This demonstrates how to use the year picker in both standalone and dialog modes.
 */
@Composable
fun IBankYearPickerExample() {
    var selectedYear by remember { mutableIntStateOf(2024) }
    var showDialog by remember { mutableStateOf(false) }

    Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .padding(16.dp)
        ) {
            Text(text = "Selected Year: $selectedYear")

            Button(
                onClick = { showDialog = true },
                modifier = Modifier.padding(top = 16.dp)
            ) {
                Text("Open Year Picker Dialog")
            }

            // Example of standalone year picker
            IBankYearPicker(
                modifier = Modifier.padding(top = 16.dp),
                selectedYear = selectedYear,
                config = DatePickerConfig.build {
                    // Example: Limit years from 2020 to 2030
                    minDate = Calendar.getInstance().apply { set(2020, 0, 1) }.time
                    maxDate = Calendar.getInstance().apply { set(2030, 11, 31) }.time
                },
                onYearSelected = { year ->
                    selectedYear = year
                },
                onCancel = {
                    // Handle cancel action
                },
                onClose = {
                    // Handle close action
                }
            )
        }

        // Example of dialog usage
        if (showDialog) {
            IBankYearPickerDialog(
                selectedYear = selectedYear,
                config = DatePickerConfig.build {
                    // Example: Limit years from 2020 to 2030
                    minDate = Calendar.getInstance().apply { set(2020, 0, 1) }.time
                    maxDate = Calendar.getInstance().apply { set(2030, 11, 31) }.time
                },
                onDismissRequest = {
                    showDialog = false
                },
                onYearSelected = { year ->
                    selectedYear = year
                    showDialog = false
                },
                onNegativeAction = {
                    showDialog = false
                }
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewIBankYearPickerExample() {
    IBankYearPickerExample()
}
